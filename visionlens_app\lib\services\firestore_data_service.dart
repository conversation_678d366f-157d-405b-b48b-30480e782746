import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/product.dart';
import '../models/category.dart' as category_model;
import 'firebase_real_service.dart';
import 'firebase_web_service.dart';

/// خدمة البيانات باستخدام Firestore - مشاركة البيانات بين جميع المستخدمين
class FirestoreDataService {
  static FirebaseFirestore? get _firestore => FirebaseRealService.firestore;

  // ==================== إدارة المنتجات ====================

  /// جلب قائمة المنتجات من Firestore
  static Future<List<Product>> getProducts() async {
    try {
      print('🔄 [${DateTime.now()}] محاولة جلب المنتجات من Firestore...');

      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        print('🌐 [${DateTime.now()}] استخدام Firebase Web Service...');
        final webProducts = await FirebaseWebService.getProducts();
        print(
          '🌐 [${DateTime.now()}] تم استلام ${webProducts.length} منتج من Firebase Web Service',
        );

        for (int i = 0; i < webProducts.length && i < 3; i++) {
          print(
            '🌐 منتج ${i + 1}: ${webProducts[i].name} (${webProducts[i].id})',
          );
        }

        return webProducts;
      }

      // للمنصات الأخرى: استخدم Firebase الحقيقي
      if (_firestore == null) {
        print('⏳ [${DateTime.now()}] انتظار تهيئة Firebase...');
        await FirebaseRealService.initialize();

        // انتظار إضافي للتأكد
        await Future.delayed(const Duration(milliseconds: 1000));

        if (_firestore == null) {
          print(
            '❌ [${DateTime.now()}] Firestore غير متاح، لم يتم تهيئة Firebase بشكل صحيح',
          );
          return [];
        }
      }

      print('🔗 [${DateTime.now()}] الاتصال بـ Firestore...');
      print('🌐 [${DateTime.now()}] Project ID: visionlens-app-5ab70');

      final snapshot = await _firestore!.collection('products').get();

      print(
        '📊 [${DateTime.now()}] عدد المستندات المسترجعة: ${snapshot.docs.length}',
      );
      print(
        '📊 [${DateTime.now()}] حالة الشبكة: ${snapshot.metadata.isFromCache ? 'من Cache' : 'من الخادم'}',
      );

      final products = snapshot.docs
          .map((doc) => Product.fromJson({...doc.data(), 'id': doc.id}))
          .toList();

      print(
        '📦 [${DateTime.now()}] تم جلب ${products.length} منتج من Firestore بنجاح',
      );
      for (final product in products) {
        print('  - ${product.name} (${product.id})');
      }

      return products;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب المنتجات من Firestore: $e');
        print('❌ نوع الخطأ: ${e.runtimeType}');
      }
      return [];
    }
  }

  /// إضافة منتج جديد إلى Firestore
  static Future<void> addProduct(Product product) async {
    try {
      if (kDebugMode) {
        print('🔄 محاولة إضافة منتج إلى Firestore: ${product.name}');
        print('🔄 معرف المنتج: ${product.id}');
      }

      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        print('🌐 استخدام Firebase Web Service لإضافة المنتج...');
        final success = await FirebaseWebService.addProduct(product);
        if (!success) {
          throw Exception('فشل في إضافة المنتج عبر Firebase Web Service');
        }
        return;
      }

      // للمنصات الأخرى: استخدم Firebase الحقيقي
      if (_firestore == null) {
        if (kDebugMode) {
          print('❌ Firestore غير متاح - لم يتم تهيئة Firebase بشكل صحيح');
        }
        throw Exception('Firestore غير متاح');
      }

      final productData = product.toJson();
      if (kDebugMode) {
        print('📝 بيانات المنتج: $productData');
      }

      await _firestore!.collection('products').doc(product.id).set(productData);

      if (kDebugMode) {
        print('✅ تم إضافة المنتج إلى Firestore بنجاح: ${product.name}');
        print(
          '🔗 رابط Firestore: https://console.firebase.google.com/project/visionlens-app-5ab70/firestore',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إضافة المنتج إلى Firestore: $e');
        print('❌ نوع الخطأ: ${e.runtimeType}');
      }
      rethrow;
    }
  }

  /// تعديل منتج في Firestore
  static Future<void> updateProduct(Product product) async {
    try {
      if (kDebugMode) {
        print('🔄 محاولة تعديل منتج في Firestore: ${product.name}');
      }

      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        print('🌐 استخدام Firebase Web Service لتعديل المنتج...');
        final success = await FirebaseWebService.updateProduct(product);
        if (!success) {
          throw Exception('فشل في تعديل المنتج عبر Firebase Web Service');
        }
        return;
      }

      // للمنصات الأخرى: استخدم Firebase الحقيقي
      if (_firestore == null) {
        throw Exception('Firestore غير متاح');
      }

      final productData = product.toJson();
      await _firestore!
          .collection('products')
          .doc(product.id)
          .update(productData);

      if (kDebugMode) {
        print('✅ تم تعديل المنتج في Firestore بنجاح: ${product.name}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تعديل المنتج في Firestore: $e');
      }
      rethrow;
    }
  }

  /// حذف منتج من Firestore
  static Future<void> deleteProduct(String productId) async {
    try {
      if (kDebugMode) {
        print('🔄 محاولة حذف منتج من Firestore: $productId');
      }

      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        print('🌐 استخدام Firebase Web Service لحذف المنتج...');
        final success = await FirebaseWebService.deleteProduct(productId);
        if (!success) {
          throw Exception('فشل في حذف المنتج عبر Firebase Web Service');
        }
        return;
      }

      // للمنصات الأخرى: استخدم Firebase الحقيقي
      if (_firestore == null) {
        throw Exception('Firestore غير متاح');
      }

      await _firestore!.collection('products').doc(productId).delete();

      if (kDebugMode) {
        print('✅ تم حذف المنتج من Firestore بنجاح: $productId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في حذف المنتج من Firestore: $e');
      }
      rethrow;
    }
  }

  /// البحث في المنتجات
  static Future<List<Product>> searchProducts(String query) async {
    final products = await getProducts();

    if (query.isEmpty) {
      return products;
    }

    return products.where((product) {
      return product.name.toLowerCase().contains(query.toLowerCase()) ||
          product.description.toLowerCase().contains(query.toLowerCase()) ||
          (product.brand?.toLowerCase().contains(query.toLowerCase()) ?? false);
    }).toList();
  }

  /// جلب المنتجات حسب الفئة
  static Future<List<Product>> getProductsByCategory(String categoryId) async {
    try {
      if (_firestore == null) return [];

      final snapshot = await _firestore!
          .collection('products')
          .where('categoryId', isEqualTo: categoryId)
          .get();

      return snapshot.docs
          .map((doc) => Product.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب المنتجات حسب الفئة: $e');
      }
      return [];
    }
  }

  /// جلب المنتجات المميزة
  static Future<List<Product>> getFeaturedProducts() async {
    try {
      if (_firestore == null) return [];

      final snapshot = await _firestore!
          .collection('products')
          .where('isFeatured', isEqualTo: true)
          .get();

      return snapshot.docs
          .map((doc) => Product.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب المنتجات المميزة: $e');
      }
      return [];
    }
  }

  // ==================== إدارة الفئات ====================

  /// جلب قائمة الفئات من Firestore
  static Future<List<category_model.Category>> getCategories() async {
    try {
      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        print('🌐 استخدام Firebase Web Service لجلب الفئات...');
        final categoriesData = await FirebaseWebService.getCategories();

        final categories = categoriesData
            .map((data) => category_model.Category.fromJson(data))
            .toList();

        if (kDebugMode) {
          print('📂 تم جلب ${categories.length} فئة من Firebase Web Service');
        }

        return categories;
      }

      // للمنصات الأخرى: استخدم Firebase الحقيقي
      if (_firestore == null) {
        if (kDebugMode) {
          print('⚠️ Firestore غير متاح، استخدام البيانات المحلية');
        }
        return [];
      }

      final snapshot = await _firestore!.collection('categories').get();
      final categories = snapshot.docs
          .map(
            (doc) =>
                category_model.Category.fromJson({...doc.data(), 'id': doc.id}),
          )
          .toList();

      if (kDebugMode) {
        print('📂 تم جلب ${categories.length} فئة من Firestore');
      }

      return categories;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب الفئات من Firestore: $e');
      }
      return [];
    }
  }

  /// إضافة فئة جديدة إلى Firestore
  static Future<void> addCategory(category_model.Category category) async {
    try {
      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        print('🌐 استخدام Firebase Web Service لإضافة الفئة...');
        final success = await FirebaseWebService.addCategory(category.toJson());
        if (!success) {
          throw Exception('فشل في إضافة الفئة عبر Firebase Web Service');
        }
        return;
      }

      // للمنصات الأخرى: استخدم Firebase الحقيقي
      if (_firestore == null) {
        if (kDebugMode) {
          print('⚠️ Firestore غير متاح');
        }
        return;
      }

      await _firestore!
          .collection('categories')
          .doc(category.id)
          .set(category.toJson());

      if (kDebugMode) {
        print('✅ تم إضافة الفئة إلى Firestore: ${category.name}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إضافة الفئة إلى Firestore: $e');
      }
      rethrow;
    }
  }

  /// تحديث فئة في Firestore
  static Future<void> updateCategory(category_model.Category category) async {
    try {
      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        print('🌐 استخدام Firebase Web Service لتعديل الفئة...');
        final success = await FirebaseWebService.updateCategory(
          category.toJson(),
        );
        if (!success) {
          throw Exception('فشل في تعديل الفئة عبر Firebase Web Service');
        }
        return;
      }

      // للمنصات الأخرى: استخدم Firebase الحقيقي
      if (_firestore == null) {
        if (kDebugMode) {
          print('⚠️ Firestore غير متاح');
        }
        return;
      }

      await _firestore!
          .collection('categories')
          .doc(category.id)
          .update(category.toJson());

      if (kDebugMode) {
        print('✅ تم تحديث الفئة في Firestore: ${category.name}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحديث الفئة في Firestore: $e');
      }
      rethrow;
    }
  }

  /// حذف فئة من Firestore
  static Future<void> deleteCategory(String categoryId) async {
    try {
      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        print('🌐 استخدام Firebase Web Service لحذف الفئة...');
        final success = await FirebaseWebService.deleteCategory(categoryId);
        if (!success) {
          throw Exception('فشل في حذف الفئة عبر Firebase Web Service');
        }
        return;
      }

      // للمنصات الأخرى: استخدم Firebase الحقيقي
      if (_firestore == null) {
        if (kDebugMode) {
          print('⚠️ Firestore غير متاح');
        }
        return;
      }

      await _firestore!.collection('categories').doc(categoryId).delete();

      if (kDebugMode) {
        print('✅ تم حذف الفئة من Firestore: $categoryId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في حذف الفئة من Firestore: $e');
      }
      rethrow;
    }
  }

  // ==================== مزامنة البيانات ====================

  /// مزامنة البيانات المحلية مع Firestore
  static Future<void> syncLocalDataToFirestore() async {
    try {
      if (_firestore == null) {
        if (kDebugMode) print('⚠️ Firestore غير متاح للمزامنة');
        return;
      }

      if (kDebugMode) {
        print('🔄 بدء مزامنة البيانات المحلية مع Firestore...');
      }

      // مزامنة المنتجات المحلية
      // يمكن إضافة هذا لاحقاً إذا احتجنا

      if (kDebugMode) {
        print('✅ تمت مزامنة البيانات مع Firestore');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في مزامنة البيانات: $e');
      }
    }
  }

  /// التحقق من اتصال Firestore
  static Future<bool> isFirestoreAvailable() async {
    try {
      if (_firestore == null) return false;
      await _firestore!.collection('test').limit(1).get();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// === إدارة الطلبات ===

  /// جلب جميع الطلبات من Firestore
  static Future<List<Map<String, dynamic>>> getOrders() async {
    try {
      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        print('🌐 استخدام Firebase Web Service لجلب الطلبات...');
        return await FirebaseWebService.getOrders();
      }

      // للمنصات الأخرى: استخدم Firebase الحقيقي
      if (_firestore == null) {
        if (kDebugMode) {
          print('⚠️ Firestore غير متاح');
        }
        return [];
      }

      final snapshot = await _firestore!.collection('orders').get();
      final orders = snapshot.docs
          .map((doc) => {...doc.data(), 'id': doc.id})
          .toList();

      if (kDebugMode) {
        print('📦 تم جلب ${orders.length} طلب من Firestore');
      }

      return orders;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب الطلبات من Firestore: $e');
      }
      return [];
    }
  }

  /// جلب المستخدمين من Firestore
  static Future<List<Map<String, dynamic>>> getUsers() async {
    try {
      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        print('🌐 استخدام Firebase Web Service لجلب المستخدمين...');
        return await FirebaseWebService.getUsers();
      }

      // للمنصات الأخرى: استخدم Firebase الحقيقي
      if (_firestore == null) {
        if (kDebugMode) {
          print('⚠️ Firestore غير متاح');
        }
        return [];
      }

      final snapshot = await _firestore!.collection('users').get();
      final users = snapshot.docs
          .map((doc) => {...doc.data(), 'id': doc.id})
          .toList();

      if (kDebugMode) {
        print('👥 تم جلب ${users.length} مستخدم من Firestore');
      }

      return users;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب المستخدمين من Firestore: $e');
      }
      return [];
    }
  }

  /// إضافة طلب جديد إلى Firestore
  static Future<void> addOrder(Map<String, dynamic> orderData) async {
    try {
      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        print('🌐 استخدام Firebase Web Service لإضافة الطلب...');
        final success = await FirebaseWebService.addOrder(orderData);
        if (!success) {
          throw Exception('فشل في إضافة الطلب عبر Firebase Web Service');
        }
        return;
      }

      // للمنصات الأخرى: استخدم Firebase الحقيقي
      if (_firestore == null) {
        if (kDebugMode) {
          print('⚠️ Firestore غير متاح');
        }
        return;
      }

      await _firestore!.collection('orders').add(orderData);

      if (kDebugMode) {
        print('✅ تم إضافة الطلب إلى Firestore: ${orderData['id']}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إضافة الطلب إلى Firestore: $e');
      }
      rethrow;
    }
  }

  /// === إدارة البراندات ===

  /// جلب جميع البراندات من Firestore
  static Future<List<Map<String, dynamic>>> getBrands() async {
    try {
      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        print('🌐 استخدام Firebase Web Service لجلب البراندات...');
        return await FirebaseWebService.getBrands();
      }

      // للمنصات الأخرى: استخدم Firebase الحقيقي
      if (_firestore == null) {
        if (kDebugMode) {
          print('⚠️ Firestore غير متاح');
        }
        return [];
      }

      final snapshot = await _firestore!.collection('brands').get();
      final brands = snapshot.docs
          .map((doc) => {...doc.data(), 'id': doc.id})
          .toList();

      if (kDebugMode) {
        print('🏷️ تم جلب ${brands.length} براند من Firestore');
      }

      return brands;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب البراندات من Firestore: $e');
      }
      return [];
    }
  }

  /// إضافة براند جديد
  static Future<void> addBrand(Map<String, dynamic> brandData) async {
    try {
      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        print('🌐 استخدام Firebase Web Service لإضافة البراند...');
        final success = await FirebaseWebService.addBrand(brandData);
        if (!success) {
          throw Exception('فشل في إضافة البراند عبر Firebase Web Service');
        }
        return;
      }

      // للمنصات الأخرى: استخدم Firebase الحقيقي
      if (_firestore == null) {
        if (kDebugMode) {
          print('⚠️ Firestore غير متاح');
        }
        return;
      }

      await _firestore!.collection('brands').add(brandData);

      if (kDebugMode) {
        print('✅ تم إضافة البراند إلى Firestore: ${brandData['name']}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إضافة البراند إلى Firestore: $e');
      }
      rethrow;
    }
  }

  /// تحديث براند موجود
  static Future<void> updateBrand(Map<String, dynamic> brandData) async {
    try {
      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        print('🌐 استخدام Firebase Web Service لتحديث البراند...');
        final success = await FirebaseWebService.updateBrand(brandData);
        if (!success) {
          throw Exception('فشل في تحديث البراند عبر Firebase Web Service');
        }
        return;
      }

      // للمنصات الأخرى: استخدم Firebase الحقيقي
      if (_firestore == null) {
        if (kDebugMode) {
          print('⚠️ Firestore غير متاح');
        }
        return;
      }

      await _firestore!
          .collection('brands')
          .doc(brandData['id'])
          .update(brandData);

      if (kDebugMode) {
        print('✅ تم تحديث البراند في Firestore: ${brandData['name']}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحديث البراند في Firestore: $e');
      }
      rethrow;
    }
  }

  /// حذف براند
  static Future<void> deleteBrand(String brandId) async {
    try {
      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        print('🌐 استخدام Firebase Web Service لحذف البراند...');
        final success = await FirebaseWebService.deleteBrand(brandId);
        if (!success) {
          throw Exception('فشل في حذف البراند عبر Firebase Web Service');
        }
        return;
      }

      // للمنصات الأخرى: استخدم Firebase الحقيقي
      if (_firestore == null) {
        if (kDebugMode) {
          print('⚠️ Firestore غير متاح');
        }
        return;
      }

      await _firestore!.collection('brands').doc(brandId).delete();

      if (kDebugMode) {
        print('✅ تم حذف البراند من Firestore: $brandId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في حذف البراند من Firestore: $e');
      }
      rethrow;
    }
  }
}
