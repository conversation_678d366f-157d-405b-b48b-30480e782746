import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../app_properties.dart';
import '../../services/app_state.dart';

class PrescriptionPage extends StatefulWidget {
  const PrescriptionPage({super.key});

  @override
  State<PrescriptionPage> createState() => _PrescriptionPageState();
}

class _PrescriptionPageState extends State<PrescriptionPage> {
  final AppState _appState = AppState();
  final _formKey = GlobalKey<FormState>();
  
  // متحكمات النص للعين اليمنى
  final _rightSphereController = TextEditingController();
  final _rightCylinderController = TextEditingController();
  final _rightAxisController = TextEditingController();
  final _rightAddController = TextEditingController();
  
  // متحكمات النص للعين اليسرى
  final _leftSphereController = TextEditingController();
  final _leftCylinderController = TextEditingController();
  final _leftAxisController = TextEditingController();
  final _leftAddController = TextEditingController();
  
  // متحكمات إضافية
  final _pdController = TextEditingController();
  final _doctorNameController = TextEditingController();
  final _notesController = TextEditingController();
  
  DateTime? _prescriptionDate;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadPrescription();
  }

  Future<void> _loadPrescription() async {
    // محاكاة تحميل الوصفة المحفوظة
    await Future.delayed(const Duration(milliseconds: 500));
    
    // يمكن تحميل البيانات المحفوظة هنا
    setState(() {
      _prescriptionDate = DateTime.now().subtract(const Duration(days: 30));
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الوصفة الطبية'),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _savePrescription,
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            _buildInfoCard(),
            const SizedBox(height: 16),
            _buildEyeSection('العين اليمنى (OD)', true),
            const SizedBox(height: 16),
            _buildEyeSection('العين اليسرى (OS)', false),
            const SizedBox(height: 16),
            _buildAdditionalInfo(),
            const SizedBox(height: 24),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.info_outline, color: AppColors.primaryColor),
                const SizedBox(width: 8),
                Text(
                  'معلومات الوصفة',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              'يرجى إدخال بيانات الوصفة الطبية كما هي مكتوبة من قبل طبيب العيون. '
              'هذه البيانات ستساعدنا في تقديم أفضل المنتجات المناسبة لحالتك.',
              style: TextStyle(color: AppColors.secondaryText),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                const Icon(Icons.calendar_today, size: 16, color: AppColors.secondaryText),
                const SizedBox(width: 8),
                Text(
                  _prescriptionDate != null
                      ? 'تاريخ الوصفة: ${_formatDate(_prescriptionDate!)}'
                      : 'لم يتم تحديد تاريخ الوصفة',
                  style: const TextStyle(color: AppColors.secondaryText),
                ),
                const Spacer(),
                TextButton(
                  onPressed: _selectDate,
                  child: const Text('تحديد التاريخ'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEyeSection(String title, bool isRight) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildPrescriptionField(
                    'SPH (كروي)',
                    isRight ? _rightSphereController : _leftSphereController,
                    'مثال: -2.50',
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildPrescriptionField(
                    'CYL (أسطواني)',
                    isRight ? _rightCylinderController : _leftCylinderController,
                    'مثال: -1.00',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildPrescriptionField(
                    'AXIS (المحور)',
                    isRight ? _rightAxisController : _leftAxisController,
                    'مثال: 90',
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildPrescriptionField(
                    'ADD (إضافة)',
                    isRight ? _rightAddController : _leftAddController,
                    'مثال: +2.00',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrescriptionField(String label, TextEditingController controller, String hint) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        border: const OutlineInputBorder(),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      keyboardType: const TextInputType.numberWithOptions(decimal: true, signed: true),
    );
  }

  Widget _buildAdditionalInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات إضافية',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            _buildPrescriptionField(
              'PD (المسافة بين البؤبؤين)',
              _pdController,
              'مثال: 63',
            ),
            const SizedBox(height: 12),
            TextFormField(
              controller: _doctorNameController,
              decoration: const InputDecoration(
                labelText: 'اسم الطبيب',
                hintText: 'د. أحمد محمد',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
            ),
            const SizedBox(height: 12),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'ملاحظات إضافية',
                hintText: 'أي ملاحظات أو تعليمات خاصة',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _savePrescription,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryColor,
              foregroundColor: AppColors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: _isLoading
                ? const CircularProgressIndicator(color: AppColors.white)
                : const Text('حفظ الوصفة'),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton(
            onPressed: _uploadPrescriptionImage,
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.primaryColor,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.camera_alt),
                SizedBox(width: 8),
                Text('رفع صورة الوصفة'),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _prescriptionDate ?? DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 365 * 5)),
      lastDate: DateTime.now(),
    );

    if (date != null) {
      setState(() {
        _prescriptionDate = date;
      });
    }
  }

  Future<void> _savePrescription() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // محاكاة حفظ الوصفة
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ الوصفة الطبية بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ الوصفة: $e'),
            backgroundColor: AppColors.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _uploadPrescriptionImage() async {
    // محاكاة رفع صورة
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('رفع صورة الوصفة'),
        content: const Text('هذه الميزة ستكون متاحة قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  @override
  void dispose() {
    _rightSphereController.dispose();
    _rightCylinderController.dispose();
    _rightAxisController.dispose();
    _rightAddController.dispose();
    _leftSphereController.dispose();
    _leftCylinderController.dispose();
    _leftAxisController.dispose();
    _leftAddController.dispose();
    _pdController.dispose();
    _doctorNameController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}
