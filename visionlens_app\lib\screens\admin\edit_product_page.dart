import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'dart:convert';
import '../../app_properties.dart';
import '../../models/product.dart';
import '../../models/category.dart' as category_model;
import '../../api_service_mock.dart';
import '../../services/firestore_data_service.dart';

class EditProductPage extends StatefulWidget {
  final Product product;

  const EditProductPage({super.key, required this.product});

  @override
  State<EditProductPage> createState() => _EditProductPageState();
}

class _EditProductPageState extends State<EditProductPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _stockController = TextEditingController();
  final _brandController = TextEditingController();
  final _skuController = TextEditingController();

  List<category_model.Category> _categories = [];
  String? _selectedCategoryId;
  bool _isLoading = false;
  bool _isActive = true;
  bool _isFeatured = false;

  // متغيرات الصور
  final ImagePicker _picker = ImagePicker();
  XFile? _mainImage;
  final List<XFile> _additionalImages = [];
  String _existingMainImage = '';
  List<String> _existingAdditionalImages = [];
  final int _maxImages = 5;

  @override
  void initState() {
    super.initState();
    _loadCategories();
    _initializeFields();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _stockController.dispose();
    _brandController.dispose();
    _skuController.dispose();
    super.dispose();
  }

  void _initializeFields() {
    _nameController.text = widget.product.name;
    _descriptionController.text = widget.product.description;
    _priceController.text = widget.product.price.toString();
    _stockController.text = widget.product.stock.toString();
    _brandController.text = widget.product.brand ?? '';
    _skuController.text = widget.product.tags ?? '';
    _selectedCategoryId = widget.product.categoryId;
    _isActive = widget.product.inStock;
    _isFeatured = widget.product.isFeatured;
    _existingMainImage = widget.product.image;
    _existingAdditionalImages = List.from(widget.product.images);
  }

  Future<void> _loadCategories() async {
    try {
      final categories = await ApiService.getCategories();
      setState(() {
        _categories = categories;
      });
    } catch (e) {
      if (kDebugMode) print('خطأ في تحميل الفئات: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: const Text('تعديل المنتج'),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.white,
        elevation: 0,
        centerTitle: true,
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveProduct,
            child: const Text(
              'حفظ',
              style: TextStyle(
                color: AppColors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات أساسية
              _buildSectionCard(
                title: 'المعلومات الأساسية',
                children: [
                  _buildTextField(
                    controller: _nameController,
                    label: 'اسم المنتج',
                    hint: 'أدخل اسم المنتج',
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال اسم المنتج';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  _buildTextField(
                    controller: _descriptionController,
                    label: 'وصف المنتج',
                    hint: 'أدخل وصف تفصيلي للمنتج',
                    maxLines: 4,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال وصف المنتج';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  _buildTextField(
                    controller: _brandController,
                    label: 'العلامة التجارية',
                    hint: 'أدخل اسم العلامة التجارية',
                  ),
                  const SizedBox(height: 16),
                  _buildTextField(
                    controller: _skuController,
                    label: 'رمز المنتج (SKU)',
                    hint: 'أدخل رمز المنتج الفريد',
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // السعر والمخزون
              _buildSectionCard(
                title: 'السعر والمخزون',
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _buildTextField(
                          controller: _priceController,
                          label: 'السعر (IQD)',
                          hint: '0',
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'يرجى إدخال السعر';
                            }
                            if (double.tryParse(value) == null) {
                              return 'يرجى إدخال رقم صحيح';
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    String? hint,
    int maxLines = 1,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        border: const OutlineInputBorder(),
        focusedBorder: const OutlineInputBorder(
          borderSide: BorderSide(color: AppColors.primaryColor),
        ),
      ),
      maxLines: maxLines,
      keyboardType: keyboardType,
      validator: validator,
    );
  }

  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // البحث عن اسم الفئة
      final selectedCategory = _categories.firstWhere(
        (cat) => cat.id == _selectedCategoryId,
        orElse: () => _categories.first,
      );

      // تحديد الصور النهائية
      String finalMainImage = _existingMainImage;
      List<String> finalAdditionalImages = List.from(_existingAdditionalImages);

      // إذا تم اختيار صورة رئيسية جديدة
      if (_mainImage != null) {
        finalMainImage = await _imageToBase64(_mainImage!);
      }

      // إضافة الصور الإضافية الجديدة
      for (XFile image in _additionalImages) {
        final imageBase64 = await _imageToBase64(image);
        if (imageBase64.isNotEmpty) {
          finalAdditionalImages.add(imageBase64);
        }
      }

      // إنشاء منتج محدث
      final updatedProduct = widget.product.copyWith(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        price: double.parse(_priceController.text),
        originalPrice: double.parse(_priceController.text),
        image: finalMainImage,
        images: finalAdditionalImages,
        categoryId: _selectedCategoryId!,
        categoryName: selectedCategory.name,
        inStock: int.parse(_stockController.text) > 0,
        stockQuantity: int.parse(_stockController.text),
        brand: _brandController.text.trim().isEmpty
            ? null
            : _brandController.text.trim(),
        stock: int.parse(_stockController.text),
        updatedAt: DateTime.now(),
        isFeatured: _isFeatured,
        tags: _skuController.text.trim().isEmpty
            ? null
            : _skuController.text.trim(),
      );

      // محاكاة حفظ المنتج
      await Future.delayed(const Duration(seconds: 1));

      // حفظ المنتج في Firestore أولاً
      try {
        await FirestoreDataService.updateProduct(updatedProduct);
        if (kDebugMode) {
          print('تم تحديث المنتج في Firestore: ${updatedProduct.name}');
        }
      } catch (e) {
        if (kDebugMode) {
          print('خطأ في تحديث المنتج في Firestore: $e');
        }
        // في حالة فشل Firestore، احفظ محلياً
        await ApiService.updateProduct(updatedProduct);
        if (kDebugMode) {
          print('تم تحديث المنتج محلياً: ${updatedProduct.name}');
        }
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث المنتج بنجاح'),
            backgroundColor: AppColors.successColor,
          ),
        );
        Navigator.pop(context, true); // إرجاع true للإشارة إلى نجاح التحديث
      }
    } catch (e) {
      if (kDebugMode) print('خطأ في تحديث المنتج: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ أثناء تحديث المنتج'),
            backgroundColor: AppColors.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // تحويل الصور إلى base64 للحفظ
  Future<String> _imageToBase64(XFile image) async {
    try {
      final bytes = await image.readAsBytes();
      return base64Encode(bytes);
    } catch (e) {
      if (kDebugMode) print('خطأ في تحويل الصورة: $e');
      return '';
    }
  }
}
