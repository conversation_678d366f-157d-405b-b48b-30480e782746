import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../app_properties.dart';
import '../../services/data_service.dart';
import '../../services/firestore_data_service.dart';

class OrdersManagementPage extends StatefulWidget {
  const OrdersManagementPage({super.key});

  @override
  State<OrdersManagementPage> createState() => _OrdersManagementPageState();
}

class _OrdersManagementPageState extends State<OrdersManagementPage> {
  List<Map<String, dynamic>> _orders = [];
  bool _isLoading = true;
  String _selectedStatus = 'الكل';
  String _searchQuery = '';

  final List<String> _orderStatuses = [
    'الكل',
    'في الانتظار',
    'تم التأكيد',
    'قيد التحضير',
    'جاهز للشحن',
    'تم الشحن',
    'تم التسليم',
    'ملغي',
  ];

  @override
  void initState() {
    super.initState();
    _loadOrders();
  }

  String _translateStatus(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'في الانتظار';
      case 'confirmed':
        return 'مؤكد';
      case 'processing':
        return 'قيد التحضير';
      case 'shipped':
        return 'تم الشحن';
      case 'delivered':
        return 'تم التسليم';
      case 'cancelled':
        return 'ملغي';
      default:
        return status;
    }
  }

  Future<void> _loadOrders() async {
    try {
      // جلب الطلبات من Firestore أولاً
      List<Map<String, dynamic>> ordersData = [];
      try {
        ordersData = await FirestoreDataService.getOrders();
        if (kDebugMode) {
          print('✅ تم جلب ${ordersData.length} طلب من Firestore');
          for (var order in ordersData) {
            print(
              '📦 طلب: ${order['id']} - العميل: ${order['customerName']} - الحالة: ${order['status']}',
            );
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ فشل في جلب الطلبات من Firestore: $e');
          print('🔄 التبديل للبيانات المحلية...');
        }
        // في حالة فشل Firestore، استخدم البيانات المحلية
        ordersData = await DataService.getOrders();
      }

      setState(() {
        _orders = ordersData.map((orderData) {
          // تحويل البيانات إلى التنسيق المطلوب
          return {
            'id': orderData['id'] ?? '',
            'customerName': orderData['customerName'] ?? '',
            'customerEmail': orderData['customerEmail'] ?? '',
            'customerPhone': orderData['customerPhone'] ?? '',
            'status': _translateStatus(orderData['status'] ?? 'pending'),
            'total': orderData['total'] ?? 0,
            'currency': orderData['currency'] ?? 'IQD',
            'subtotal': orderData['subtotal'] ?? 0,
            'shippingCost': orderData['shippingCost'] ?? 0,
            'paymentMethod': orderData['paymentMethod'] ?? 'الدفع عند الاستلام',
            'shippingMethod': orderData['shippingMethod'] ?? 'شحن عادي',
            'items': orderData['items'] ?? [],
            'orderDate':
                DateTime.tryParse(
                  orderData['createdAt'] ?? orderData['orderDate'] ?? '',
                ) ??
                DateTime.now(),
            'address': orderData['address'] ?? '',
            'notes': orderData['notes'],
            'userId': orderData['userId'] ?? '',
          };
        }).toList();
        _isLoading = false;
      });
    } catch (e) {
      if (kDebugMode) print('خطأ في تحميل الطلبات: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<Map<String, dynamic>> get _filteredOrders {
    var filtered = _orders;

    // فلترة حسب الحالة
    if (_selectedStatus != 'الكل') {
      filtered = filtered
          .where((order) => order['status'] == _selectedStatus)
          .toList();
    }

    // فلترة حسب البحث
    if (_searchQuery.isNotEmpty) {
      filtered = filtered
          .where(
            (order) =>
                order['id'].toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ||
                order['customerName'].toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ||
                order['customerEmail'].toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ),
          )
          .toList();
    }

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: const Text('إدارة الطلبات'),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.white,
        elevation: 0,
        centerTitle: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadOrders,
              child: Column(
                children: [
                  // شريط البحث والفلترة
                  _buildSearchAndFilter(),

                  // إحصائيات سريعة
                  _buildStatsCards(),

                  // قائمة الطلبات
                  Expanded(
                    child: _filteredOrders.isEmpty
                        ? _buildEmptyState()
                        : _buildOrdersList(),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          // شريط البحث
          Container(
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: AppColors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TextField(
              onChanged: (value) => setState(() => _searchQuery = value),
              decoration: const InputDecoration(
                hintText:
                    'البحث في الطلبات (رقم الطلب، اسم العميل، البريد الإلكتروني)...',
                prefixIcon: Icon(Icons.search, color: AppColors.grey),
                border: InputBorder.none,
                contentPadding: EdgeInsets.all(16),
              ),
            ),
          ),

          const SizedBox(height: 12),

          // فلتر الحالة
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: AppColors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: _selectedStatus,
                isExpanded: true,
                hint: const Text('فلترة حسب الحالة'),
                items: _orderStatuses.map((status) {
                  return DropdownMenuItem(value: status, child: Text(status));
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedStatus = value ?? 'الكل';
                  });
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsCards() {
    final totalOrders = _orders.length;
    final pendingOrders = _orders
        .where((o) => o['status'] == 'في الانتظار')
        .length;
    final shippedOrders = _orders
        .where((o) => o['status'] == 'تم الشحن')
        .length;
    final deliveredOrders = _orders
        .where((o) => o['status'] == 'تم التسليم')
        .length;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'إجمالي الطلبات',
              totalOrders.toString(),
              Icons.shopping_cart,
              AppColors.primaryColor,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              'في الانتظار',
              pendingOrders.toString(),
              Icons.pending,
              AppColors.warningColor,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              'تم الشحن',
              shippedOrders.toString(),
              Icons.local_shipping,
              AppColors.primaryColor,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              'تم التسليم',
              deliveredOrders.toString(),
              Icons.check_circle,
              AppColors.successColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 6),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: const TextStyle(
              fontSize: 10,
              color: AppColors.secondaryText,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.shopping_cart_outlined,
            size: 80,
            color: AppColors.grey.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          const Text(
            'لا توجد طلبات',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.secondaryText,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isNotEmpty || _selectedStatus != 'الكل'
                ? 'لا توجد طلبات تطابق معايير البحث'
                : 'لم يتم تقديم أي طلبات بعد',
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.secondaryText,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildOrdersList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredOrders.length,
      itemBuilder: (context, index) {
        final order = _filteredOrders[index];
        return _buildOrderCard(order);
      },
    );
  }

  Widget _buildOrderCard(Map<String, dynamic> order) {
    final statusColor = _getStatusColor(order['status']);
    final items = order['items'] as List<dynamic>;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ExpansionTile(
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: statusColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(25),
          ),
          child: Icon(_getStatusIcon(order['status']), color: statusColor),
        ),
        title: Text(
          'طلب #${order['id']}',
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              order['customerName'],
              style: const TextStyle(
                color: AppColors.primaryText,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 2),
            Text(
              '${order['total']} ${order['currency']}',
              style: const TextStyle(
                color: AppColors.primaryColor,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    order['status'],
                    style: const TextStyle(
                      color: AppColors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  _formatDate(order['orderDate']),
                  style: const TextStyle(
                    color: AppColors.secondaryText,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // معلومات العميل
                _buildInfoSection('معلومات العميل', [
                  _buildInfoRow('الاسم', order['customerName']),
                  _buildInfoRow('البريد الإلكتروني', order['customerEmail']),
                  _buildInfoRow('الهاتف', order['customerPhone']),
                  _buildInfoRow('العنوان', order['address']),
                ]),

                const SizedBox(height: 16),

                // تفاصيل الطلب
                _buildInfoSection('تفاصيل الطلب', [
                  ...items.map(
                    (item) => _buildInfoRow(
                      item['name'],
                      '${item['quantity']} × ${item['price']} ${order['currency']}',
                    ),
                  ),
                  const Divider(),
                  _buildInfoRow(
                    'المجموع',
                    '${order['total']} ${order['currency']}',
                    isTotal: true,
                  ),
                ]),

                const SizedBox(height: 16),

                // أزرار الإجراءات
                _buildActionButtons(order),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.primaryColor,
          ),
        ),
        const SizedBox(height: 8),
        ...children,
      ],
    );
  }

  Widget _buildInfoRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(
                color: AppColors.secondaryText,
                fontSize: isTotal ? 14 : 12,
                fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: isTotal ? AppColors.primaryColor : AppColors.primaryText,
                fontSize: isTotal ? 14 : 12,
                fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(Map<String, dynamic> order) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _updateOrderStatus(order),
            icon: const Icon(Icons.edit, size: 16),
            label: const Text('تحديث الحالة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryColor,
              foregroundColor: AppColors.white,
              padding: const EdgeInsets.symmetric(vertical: 8),
            ),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => _viewOrderDetails(order),
            icon: const Icon(Icons.visibility, size: 16),
            label: const Text('عرض التفاصيل'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.primaryColor,
              padding: const EdgeInsets.symmetric(vertical: 8),
            ),
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'في الانتظار':
        return AppColors.warningColor;
      case 'تم التأكيد':
        return AppColors.primaryColor;
      case 'قيد التحضير':
        return AppColors.primaryColor;
      case 'جاهز للشحن':
        return AppColors.primaryColor;
      case 'تم الشحن':
        return AppColors.primaryColor;
      case 'تم التسليم':
        return AppColors.successColor;
      case 'ملغي':
        return AppColors.errorColor;
      default:
        return AppColors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'في الانتظار':
        return Icons.pending;
      case 'تم التأكيد':
        return Icons.check_circle;
      case 'قيد التحضير':
        return Icons.build;
      case 'جاهز للشحن':
        return Icons.inventory;
      case 'تم الشحن':
        return Icons.local_shipping;
      case 'تم التسليم':
        return Icons.done_all;
      case 'ملغي':
        return Icons.cancel;
      default:
        return Icons.help;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  void _updateOrderStatus(Map<String, dynamic> order) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحديث حالة الطلب'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('طلب #${order['id']}'),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: order['status'],
              decoration: const InputDecoration(
                labelText: 'الحالة الجديدة',
                border: OutlineInputBorder(),
              ),
              items: _orderStatuses.skip(1).map((status) {
                return DropdownMenuItem(value: status, child: Text(status));
              }).toList(),
              onChanged: (newStatus) async {
                if (newStatus != null) {
                  Navigator.pop(context);

                  try {
                    if (kDebugMode) {
                      print(
                        'محاولة تحديث حالة الطلب ${order['id']} إلى: $newStatus',
                      );
                    }

                    // تحديث الحالة في قاعدة البيانات
                    final updatedOrder = Map<String, dynamic>.from(order);
                    updatedOrder['status'] = newStatus;

                    await DataService.updateOrder(updatedOrder);

                    // تحديث الحالة في القائمة المحلية
                    setState(() {
                      order['status'] = newStatus;
                    });

                    if (kDebugMode) {
                      print('تم تحديث حالة الطلب بنجاح');
                    }

                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('تم تحديث حالة الطلب إلى: $newStatus'),
                          backgroundColor: AppColors.success,
                        ),
                      );
                    }
                  } catch (e) {
                    if (kDebugMode) {
                      print('خطأ في تحديث حالة الطلب: $e');
                    }

                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            'فشل في تحديث حالة الطلب: ${e.toString()}',
                          ),
                          backgroundColor: AppColors.errorColor,
                        ),
                      );
                    }
                  }
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _viewOrderDetails(Map<String, dynamic> order) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل الطلب #${order['id']}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildInfoSection('معلومات العميل', [
                _buildInfoRow('الاسم', order['customerName']),
                _buildInfoRow('البريد الإلكتروني', order['customerEmail']),
                _buildInfoRow('الهاتف', order['customerPhone']),
                _buildInfoRow('العنوان', order['address']),
              ]),
              const SizedBox(height: 16),
              _buildInfoSection('تفاصيل الطلب', [
                ...(order['items'] as List<dynamic>).map(
                  (item) => _buildInfoRow(
                    item['name'],
                    '${item['quantity']} × ${item['price']} ${order['currency']}',
                  ),
                ),
                const Divider(),
                _buildInfoRow(
                  'المجموع',
                  '${order['total']} ${order['currency']}',
                  isTotal: true,
                ),
              ]),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
