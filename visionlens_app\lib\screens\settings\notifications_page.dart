import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../app_properties.dart';
import '../../services/app_state.dart';
import '../../services/data_service.dart';
import '../../services/firestore_data_service.dart';

class NotificationsPage extends StatefulWidget {
  const NotificationsPage({super.key});

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage> {
  final AppState _appState = AppState();
  List<Map<String, dynamic>> _notifications = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadNotifications();
  }

  Future<void> _loadNotifications() async {
    try {
      // محاكاة تحميل الإشعارات
      await Future.delayed(const Duration(seconds: 1));

      // إنشاء إشعارات تجريبية
      final notifications = [
        {
          'id': '1',
          'title': 'تم تأكيد طلبك',
          'message': 'تم تأكيد طلبك #12345 وسيتم تحضيره قريباً',
          'type': 'order',
          'isRead': false,
          'createdAt': DateTime.now()
              .subtract(const Duration(hours: 2))
              .toIso8601String(),
          'icon': Icons.check_circle,
          'color': AppColors.success,
        },
        {
          'id': '2',
          'title': 'عرض خاص',
          'message': 'خصم 20% على جميع العدسات اللاصقة لفترة محدودة',
          'type': 'promotion',
          'isRead': false,
          'createdAt': DateTime.now()
              .subtract(const Duration(days: 1))
              .toIso8601String(),
          'icon': Icons.local_offer,
          'color': AppColors.frameGold,
        },
        {
          'id': '3',
          'title': 'تم شحن طلبك',
          'message': 'طلبك #12344 في الطريق إليك، سيصل خلال 2-3 أيام',
          'type': 'shipping',
          'isRead': true,
          'createdAt': DateTime.now()
              .subtract(const Duration(days: 2))
              .toIso8601String(),
          'icon': Icons.local_shipping,
          'color': AppColors.primaryColor,
        },
        {
          'id': '4',
          'title': 'منتجات جديدة',
          'message': 'تم إضافة مجموعة جديدة من النظارات الطبية',
          'type': 'product',
          'isRead': true,
          'createdAt': DateTime.now()
              .subtract(const Duration(days: 3))
              .toIso8601String(),
          'icon': Icons.new_releases,
          'color': AppColors.primaryColor,
        },
        {
          'id': '5',
          'title': 'تذكير الفحص',
          'message': 'حان وقت فحص النظر الدوري، احجز موعدك الآن',
          'type': 'reminder',
          'isRead': true,
          'createdAt': DateTime.now()
              .subtract(const Duration(days: 7))
              .toIso8601String(),
          'icon': Icons.visibility,
          'color': AppColors.warningColor,
        },
      ];

      setState(() {
        _notifications = notifications;
        _isLoading = false;
      });
    } catch (e) {
      if (kDebugMode) print('خطأ في تحميل الإشعارات: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإشعارات'),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.white,
        actions: [
          if (_notifications.any((n) => !n['isRead']))
            TextButton(
              onPressed: _markAllAsRead,
              child: const Text(
                'قراءة الكل',
                style: TextStyle(color: AppColors.white),
              ),
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _notifications.isEmpty
          ? _buildEmptyState()
          : _buildNotificationsList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_none,
            size: 80,
            color: AppColors.grey.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد إشعارات',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(color: AppColors.secondaryText),
          ),
          const SizedBox(height: 8),
          Text(
            'ستظهر الإشعارات هنا عند وصولها',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: AppColors.secondaryText),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _notifications.length,
      itemBuilder: (context, index) {
        final notification = _notifications[index];
        return _buildNotificationCard(notification, index);
      },
    );
  }

  Widget _buildNotificationCard(Map<String, dynamic> notification, int index) {
    final isRead = notification['isRead'] ?? false;
    final title = notification['title'] ?? '';
    final message = notification['message'] ?? '';
    final createdAt = notification['createdAt'] ?? '';
    final icon = notification['icon'] ?? Icons.notifications;
    final color = notification['color'] ?? AppColors.primaryColor;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      color: isRead ? null : AppColors.lightGrey.withValues(alpha: 0.3),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(icon, color: color, size: 20),
        ),
        title: Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: isRead ? FontWeight.normal : FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              message,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: AppColors.secondaryText),
            ),
            const SizedBox(height: 8),
            Text(
              _formatDate(createdAt),
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: AppColors.secondaryText),
            ),
          ],
        ),
        trailing: !isRead
            ? Container(
                width: 8,
                height: 8,
                decoration: const BoxDecoration(
                  color: AppColors.primaryColor,
                  shape: BoxShape.circle,
                ),
              )
            : null,
        onTap: () => _markAsRead(index),
      ),
    );
  }

  void _markAsRead(int index) {
    setState(() {
      _notifications[index]['isRead'] = true;
    });
  }

  void _markAllAsRead() {
    setState(() {
      for (var notification in _notifications) {
        notification['isRead'] = true;
      }
    });
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      final now = DateTime.now();
      final difference = now.difference(date);

      if (difference.inDays > 0) {
        return 'منذ ${difference.inDays} يوم';
      } else if (difference.inHours > 0) {
        return 'منذ ${difference.inHours} ساعة';
      } else if (difference.inMinutes > 0) {
        return 'منذ ${difference.inMinutes} دقيقة';
      } else {
        return 'الآن';
      }
    } catch (e) {
      return dateString;
    }
  }
}
